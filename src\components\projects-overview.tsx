'use client';

import { useState } from 'react';
import { ProjectsList } from '@/components/projects-list';
import { NewProjectButton } from '@/components/new-project-button';
import { Search, Download, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface ProjectsOverviewProps {
  projects: any[];
}

export function ProjectsOverview({ projects }: ProjectsOverviewProps) {
  const [searchTerm, setSearchTerm] = useState('');

  // Filter projects based on search term
  const filteredProjects = projects.filter(project => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      (project.name && project.name.toLowerCase().includes(searchLower)) ||
      (project.location && project.location.toLowerCase().includes(searchLower)) ||
      (project.type && project.type.toLowerCase().includes(searchLower)) ||
      (project.client_name && project.client_name.toLowerCase().includes(searchLower))
    );
  });

  return (
    <div className="w-full">
      {/* Main container */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-slate-300 mb-8">
        {/* Header with title, search and actions */}
        <div className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200 px-8 py-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            {/* Left side - Title and search */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-5">
              <h2 className="text-3xl font-bold text-slate-900 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-violet-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                Projects
              </h2>
              <div className="relative max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-slate-400" />
                </div>
                <Input
                  type="text"
                  placeholder="Search by name, location, type or client..."
                  className="pl-11 pr-4 py-3 h-12 border border-slate-300 rounded-lg w-full sm:w-80 focus:ring-violet-500 focus:border-violet-500 bg-white shadow-sm text-base"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600"
                    onClick={() => setSearchTerm('')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Right side - Action buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="text-slate-700 border-slate-300 hover:bg-slate-50 hover:text-violet-600 transition-colors h-12 rounded-lg shadow-sm"
              >
                <Filter className="h-4 w-4 mr-2" />
                <span>Filter</span>
              </Button>
              <Button
                variant="outline"
                className="text-slate-700 border-slate-300 hover:bg-slate-50 hover:text-violet-600 transition-colors h-12 rounded-lg shadow-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                <span>Export</span>
              </Button>
              <NewProjectButton />
            </div>
          </div>
        </div>

        {/* Project count and filter indicators */}
        {searchTerm && (
          <div className="px-8 py-3 bg-gradient-to-r from-slate-100/50 to-slate-50/50 border-b border-slate-200 flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm text-slate-700 font-medium">
                Found {filteredProjects.length} {filteredProjects.length === 1 ? 'project' : 'projects'} matching: {searchTerm}
              </span>
            </div>
            <button
              className="text-xs text-violet-600 hover:text-violet-800 hover:underline font-medium"
              onClick={() => setSearchTerm('')}
            >
              Clear search
            </button>
          </div>
        )}

        {/* Projects list */}
        <ProjectsList
          projects={filteredProjects}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          showFilters={false}
          filters={{ status: '', type: '' }}
          onFiltersChange={() => {}}
          onToggleFilters={() => {}}
        />
      </div>
    </div>
  );
}
